const path = require('node:path');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');

const { urls, paymentHubConfig, env } = getEnvironmentConfig();

const OUTPUT_DIR_PATH = path.resolve(__dirname, '.tmp/public');

module.exports = (_, argv) => {
    const isProdMode = argv.mode === 'production';

    return {
        mode: isProdMode ? 'production' : 'development',

        output: {
            filename: '[name].js',
            path: OUTPUT_DIR_PATH,
            publicPath: '/',
        },

        module: {
            rules: [
                {
                    test: /\.js$/,
                    exclude: /node_modules/,
                    use: {
                        loader: 'babel-loader',
                        options: {
                            sourceMap: false,
                            presets: ['@babel/preset-env'],
                            plugins: ['angularjs-annotate'],
                        },
                    },
                },
                {
                    test: /\.scss$/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        'css-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                implementation: require('sass'),
                                sourceMap: true,
                                sassOptions: {
                                    includePaths: ['assets/bower_components', 'assets/styles'],
                                },
                            },
                        },
                    ],
                },
                {
                    test: /\.css$/,
                    use: [MiniCssExtractPlugin.loader, 'css-loader'],
                },
                {
                    test: /\.html$/,
                    use: ['html-loader'],
                },
            ],
        },

        plugins: [
            new CleanWebpackPlugin(),
            new CopyWebpackPlugin({
                patterns: [
                    // { from: './assets/bower_components', to: 'bower_components' },
                    { from: './assets/js', to: 'js' },
                    { from: './assets/images', to: 'images' },
                    { from: './assets/favicon.ico', to: '' },
                ],
            }),
            new MiniCssExtractPlugin({
                filename: '[name].css',
            }),
            new webpack.DefinePlugin({
                'process.env': {
                    ENV: JSON.stringify(env),
                    HOME_PAGE_URL: JSON.stringify(urls.home_page && urls.home_page.baseUrl || ''),
                    MAIN_APP_URL: JSON.stringify(urls.main_app && urls.main_app.baseUrl || ''),
                    ESW_URL: JSON.stringify(urls.esw && urls.esw.baseUrl || ''),
                    ESW_NEW_URL: JSON.stringify(urls.esw_new && urls.esw_new.baseUrl || ''),
                    SWT_URL: JSON.stringify(urls.swt && urls.swt.baseUrl || ''),
                    SCORES_APP_URL: JSON.stringify(urls.scores && urls.scores.baseUrl || ''),
                    SALES_HUB_URL: JSON.stringify(urls.salesHub && urls.salesHub.baseUrl || ''),
                    PAYMENT_HUB_API_HOST: JSON.stringify(paymentHubConfig && paymentHubConfig.apiUrl || ''),
                    PAYMENT_HUB_PUBLISHABLE_KEY: JSON.stringify(paymentHubConfig && paymentHubConfig.publicKey || ''),
                },
            }),
            new webpack.ProvidePlugin({
                $: 'jquery',
                jQuery: 'jquery',
                'window.jQuery': 'jquery',
                angular: 'angular',
                _: 'underscore',
                moment: 'moment'
            }),
        ],

        optimization: {
            splitChunks: {
                cacheGroups: {
                    vendorLibs: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendor-libs',
                        chunks: 'all',
                        priority: 10,
                        enforce: true,
                    },
                    angular: {
                        test: /[\\/]node_modules[\\/]angular/,
                        name: 'angular-vendor',
                        chunks: 'all',
                        priority: 20,
                        enforce: true,
                    },
                    default: false,
                    vendors: false,
                },
            },
            runtimeChunk: false,
            minimize: isProdMode,
            minimizer: [
                new TerserPlugin({
                    exclude: /bower_components/,
                }),
            ],
        },

        resolve: {
            extensions: ['.js', '.scss', '.css'],
        },

        devServer: {
            static: [
                {
                    directory: OUTPUT_DIR_PATH,
                    publicPath: '/'
                },
                {
                    directory: path.join(__dirname, 'bower_components'),
                    publicPath: '/bower_components/'
                },
            ],
            proxy: [{
                context: ['/api'],
                target: `http://localhost:3000`,
                changeOrigin: true,
            }, {
                context: ['/images/tmpl-layout'],
                target: `https://sw-files-dev.s3.amazonaws.com`,
                changeOrigin: true,
            }],
            hot: true
        },

        stats: {
            warnings: false,
        },

        ignoreWarnings: [
            {
                module: /sass-loader/,
                message: /Module Warning/,
            },
        ],
    };
};

function getEnvironmentConfig() {
    let envUrls = process.env.URLS;

    try {
        if (envUrls) {
            envUrls = JSON.parse(envUrls);
        }
    } catch (err) {
        console.log(err);
        console.error('ENV URLS has unsupported format ' + envUrls);
        envUrls = null;
    }

    const urls = envUrls || require('./config/urls').urls;
    const { paymentHub: paymentHubConfig } = require('./config/paymentHub');
    const env = process.env.ENV || 'development';

    return { urls, paymentHubConfig, env };
}
