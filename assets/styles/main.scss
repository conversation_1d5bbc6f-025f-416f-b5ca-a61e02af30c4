@import "layouts/variables";

$grid-float-breakpoint:     1000px !default;

@import "bootstrap-sass/assets/stylesheets/bootstrap.scss";

// Fix for https://github.com/FortAwesome/Font-Awesome/issues/10842
@import "layouts/fa-variables";
@import "layouts/components";
@import "modules/all";
@import "layouts/fonts";
@import "layouts/forms";
@import "layouts/main";
// import here all additional files
@import "sw/all.scss";

.browsehappy
{
   margin: 0.2em 0;
   background: #CCCCCC;
   color: #000000;
   padding: 0.2em 0;
}

.jumbotron__call-to-action
{
   margin: 1em 0;
}

.jumbotron__question
{
   font-size: 1.5em;
}


[class|="sw"]
{
   font-family: 'sportwrench';
   speak: none;
   font-style: normal;
   font-weight: normal;
   font-variant: normal;
   text-transform: none;
   line-height: 1;

   /* Better Font Rendering =========== */
   -webkit-font-smoothing: antialiased;
   -moz-osx-font-smoothing: grayscale;
}

.sw-icon-textstackbottom:before
{
   content: "\31";
}

.sw-icon-textright:before
{
   content: "\32";
}

.sw-icon-textbottom:before
{
   content: "\33";
}

.sw-icon-notext:before
{
   content: "\34";
}

.event-title {
    background-color: #e7e7e7;

    .lead {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0;
    }

    .text-sm {    
        line-height: 2;
        @media only screen and (max-width: 768px) {
            line-height: 1.5;
        }
    }
}

.main-log-navbar {
    padding: 6px 15px;
    height: auto;
    .sw-icon-textright {
        color: $blue-dark
    }
}

.sw-logo
{
   color: $blue-dark;

   &:before
   {
      font-size: $font-size-h1;
      content: "\34";

      @media screen and (min-width: $width-tablet-min)
      {
         content: "\33";
         font-size: 160px;
      }
   }
}

.jumbotron__title
{
   font-size: $font-size-h2;
   color: $blue-dark;

   @media screen and (min-width: $width-tablet-min)
   {
      display: none;
   }
}

.jumbotron__subtitle
{
   font-size: $font-size-h4;

   @media screen and (min-width: $width-tablet-min)
   {
      font-size: $font-size-h2;
   }
}

.center-block
{
   @include center-block;
   float: none;
}

.btn-social
{
   @media screen and (max-width: $screen-md-max)
   {
      margin: 10px 0;
   }
}

.text-grayout .col-paymt-status {
    color: #aaa;
}

.text-bold {
    font-weight: bold;
}

.angular-google-map-container {
    height: 300px;
}

.nav, .pagination, .carousel, .panel-title a { cursor: pointer; }

.all-teams-icon {
   color: black;
}

.status-icons, .status-icons-title {
   span {
      display: inline-block;
      vertical-align: top;
      padding: 0 2px;
      min-width: 24px;
   }
}

.bottom-pressed {
   margin-bottom: 10px;
}

.terms li {
   display: inline-block;
}

.capitalize {
  text-transform: capitalize;
}

.officials-hotel-form .glyphicon{
    color: black;
}

.assigned-ticket-btns {
    h3 {
        margin-top: 5px;
    }

    p {
        font-size: 12px;
    }

    .ticket-btn {
        padding-bottom: 10px;
        padding-top: 10px;
    }
}

.faq-page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eee;
  @media (max-width: 1000px) {
    margin: 10px 0 15px;
  }
}

.modal-w900 {
  width: 900px;
  @media (max-width: 992px) {
    width: auto;
    margin: 10px;
  }
}

.add-to-apple-wallet-button {
    height: 34px;
    cursor: pointer;
    margin-top: 20px;
}

.vendor-type {
    &-wrapper {
        text-align: end;
    }

    &-item {
        text-align: left !important;
    }

    &-label {
        @media only screen and (max-width: 768px) {
            text-align: left;
        }
    }
}

.full-width {
    width: 100%;
}

.full-width-important {
  width: 100% !important;
}

.footer {
    margin: auto auto 0 auto;
}

.to-right {
   float: right !important;
}

.btn-info-grey {
  background-color: grey;
  border-color: grey;
  color: #FFF;

  &:hover, &:active, &:focus {
    background-color: dimgrey;
    border-color: dimgrey;
    color: #FFF;
  }
}

.dropdown-scroll {
   max-height: 400px;
   overflow: scroll;
}

.signature-pad {
    border: 1px solid rgb(184, 189, 201);
    border-radius: 4px;
    display: block;
}

.signature-pad-clear {
    margin-top: 5px;
}

.has-error .signature-pad {
    border: 1px solid #a94442;
}

.merchandise-receipt {
    .merchandise-list {
        background-color: #F9FAFB;
        border-radius: 4px;
        padding: 5px 15px;
    }

    hr {
        margin: 0;
    }

    .footer {
        background: transparent;
    }
}

.remove-arrows::-webkit-outer-spin-button,
.remove-arrows::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.remove-arrows {
    -moz-appearance: textfield;
}
