const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const glob = require('glob');

const entryFiles = glob.sync([
    './frontend_admin/sport-wrench-admin.module.js',
    './frontend_admin/**/*.js',
    './assets/styles/main.scss',
    './assets/styles/admin.scss',
], { absolute: true });

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: {
        main: entryFiles,
        vendor: vendorEntry,
    },

    plugins: [
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend_admin/', from: '**/*.html', to: '[path][name][ext]' },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8087,
    },
};
