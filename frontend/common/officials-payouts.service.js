class Service {
    constructor($http, $uibModal, $httpParamSerializerJQLike, $window) {
        this.http = $http;
        this.modal = $uibModal;
        this.$httpParamSerializerJQLike = $httpParamSerializerJQLike;
        this.$window = $window;
    }

    get baseURL() {
        return '/api/event';
    }

    get(url) {
        return this.http.get(url)
            .then(({ data }) => data)
            .catch(({ data }) => Promise.reject(data));

    }

    showOfficialsRatesTable() {
        this.modal.open({
            size: 'lg',
            template: '<officials-rates-table on-close="$close()"></officials-rates-table>',
            controller: ['$scope', function($scope) {}]
        })
    }

    showDefaultAdditionalPayments(categories, memberType) {
        this.modal.open({
            size: 'sm',
            template: '<set-default-additional-payments on-close="$close()" ' +
                'categories="categories" member-type="memberType"></set-default-additional-payments>',
            controller: ['$scope', function($scope) {
                $scope.categories = categories;
                $scope.memberType = memberType;
            }]
        })
    }

    openMarkPayoutModal(eventID, eventOfficialID, info, memberType, reloadPayoutsTable) {
        const self = this;

        this.modal.open({
            size: 'md',
            backdrop: 'static',
            template: '<mark-payout on-mark-payout="onMarkPayout(data)" info="info" event-official-id="eventOfficialID" on-close="$close()"></mark-payout>',
            controller: ['$scope', function($scope) {
                $scope.info = info;

                let inProcess = false;

                $scope.onMarkPayout = (data) => {
                    if (inProcess) {
                        return;
                    }

                    inProcess = true;
    
                    self.markPayout(eventID, eventOfficialID, data, memberType)
                        .then(() => {
                            self.getMarkPayoutInfo(eventID, eventOfficialID, memberType)
                                .then(response => $scope.info = response);
                        })
                        .finally(() => inProcess = false);
                }
            }]
        }).result.then(() => {
            reloadPayoutsTable()
        })
    }

    getRates(eventID) {
        return this.get(`${this.baseURL}/${eventID}/officials/rates`);
    }

    getPayouts(eventID, type) {
        return this.get(`${this.baseURL}/${eventID}/type/${type}/payouts/list`);
    }

    saveRates(eventID, rates) {
        return this.http.put(`${this.baseURL}/${eventID}/officials/rates`, { rates });
    }

    exportCsv(eventID, data) {
        this.$window.location = `${this.baseURL}/${eventID}/staffing/payouts/export?${this.$httpParamSerializerJQLike({ data })}`
    }

    openExportModal(allowedPaymentMethodTypes, memberType) {
        this.modal.open({
            size: 'md',
            template: `<officials-payout-export
                        on-close="$close()" 
                        allowed-payment-methods="allowedPaymentMethodTypes"
                        member-type="memberType"
                        ></officials-payout-export>`,
            controller: ['$scope', function($scope) {
                $scope.allowedPaymentMethodTypes = allowedPaymentMethodTypes;
                $scope.memberType = memberType;
            }]
        })
    }

    updateAdditionalPayment(eventID, payment, type) {
        return this.http.put(`${this.baseURL}/${eventID}/type/${type}/additional-payment/update`, payment)
    }

    getAdditionalCategories(eventID) {
        return this.get(`${this.baseURL}/${eventID}/officials/additional-payment/categories`)
    }

    setDefaultAdditionalPayments({ eventID, categories, applyTo, memberType }) {
        return this.http.put(
            `${this.baseURL}/${eventID}/officials/additional-payment/set-defaults`, { categories, applyTo, memberType }
        )
    }

    getMarkPayoutInfo(eventID, eventOfficialID, type) {
        return this.get(`${this.baseURL}/${eventID}/type/${type}/${eventOfficialID}/payouts`);
    }

    markPayout(eventID, eventOfficialID, data, memberType) {
        return this.http.post(`${this.baseURL}/${eventID}/type/${memberType}/${eventOfficialID}/payouts`, data);
    }
}

Service.$inject = ['$http', '$uibModal', '$httpParamSerializerJQLike', '$window'];

angular.module('SportWrench').service('OfficialsPayoutsService', Service);
