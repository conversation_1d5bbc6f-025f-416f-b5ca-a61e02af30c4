class Service {
    constructor($http, $uibModal) {
        this.$http = $http;
        this.$uibModal = $uibModal;
    }

    get baseURL() {
        return 'api/sponsor';
    }

    get(url) {
        return this.$http.get(url)
            .then(({ data }) => data)
            .catch(({ data }) => Promise.reject(data));

    }

    getEvents() {
        return this.get(`${this.baseURL}/events`);
    }

    getEventInfo(eventID) {
        return this.get(`${this.baseURL}/event/${eventID}/info`);
    }

    getRegistrationInfo(eventID) {
        return this.get(`${this.baseURL}/event/${eventID}`);
    }

    create(eventID, data) {
        return this.$http.post(`${this.baseURL}/event/${eventID}`, data);
    }

    update(eventID, data) {
        return this.$http.put(`${this.baseURL}/event/${eventID}`, data);
    }

    openExhibitorEventRegistrationModal({ eventName, eventID, tab, modes, status, exhibitorsRegistrationIsOpened }) {
        return this.$uibModal.open({
            size: 'md',
            template: `
                <exhibitor-event-registration
                    event-name="eventName"
                    on-close-modal="$close(withReload)"
                    tab="tab"
                    event-id="eventID"
                    is-apply-mode="isApplyMode"
                    is-edit-mode="isEditMode"
                    is-view-mode="isViewMode"
                    status="status"
                    exhibitors-registration-is_opened="exhibitorsRegistrationIsOpened"
                >
                </exhibitor-event-registration>
            `,
            controller: ['$scope', function($scope) {
                $scope.eventName = eventName;
                $scope.tab = tab;
                $scope.eventID = eventID;
                $scope.isApplyMode = modes.isApply;
                $scope.isEditMode = modes.isEdit;
                $scope.isViewMode = modes.isView;
                $scope.status = status;
                $scope.exhibitorsRegistrationIsOpened = exhibitorsRegistrationIsOpened;
            }]
        }).result;
    }
}

Service.$inject = ['$http', '$uibModal'];

angular.module('SportWrench').service('ExhibitorService', Service);
