angular.module('SportWrench')

.factory('lazyLoadService', function ($ocLazyLoad, _, $injector, $providerInjector, GMAPS_API_KEY) {
    var BOWER_ROOT  = 'bower_components/';

    return {
        xslxLoader: function () {
            return $ocLazyLoad.load({
                cache: true,
                files: _.map(['js-xlsx/dist/cpexcel.js', 'js-xlsx/dist/xlsx.core.min.js'], function (route) {
                    return BOWER_ROOT + route;
                })
            });
        },
        fileUpload: function () {
            return $ocLazyLoad.load({
                name: 'angularFileUpload',
                files: [BOWER_ROOT + 'angular-file-upload/dist/angular-file-upload.min.js']
            });
        },
        loadCard: function () {
            return $ocLazyLoad.load('https://js.stripe.com/v3/');
        },
        loadReactEmailEditor: function () {
            return $ocLazyLoad.load('https://sw-files-dev.s3.us-east-1.amazonaws.com/react-email-module/static/<EMAIL>');
        },
        loadPlaid: function () {
            return $ocLazyLoad.load('https://cdn.plaid.com/link/v2/stable/link-initialize.js');
        },
        loadCKEditor: function () {
            return $ocLazyLoad.load({
                serie: true,
                cache: true,
                name: 'ngCkeditor',
                files: _.map(['ckeditor/ckeditor.js', 'ng-ckeditor/ng-ckeditor.js'], function (route) {
                    return BOWER_ROOT + route;
                })
            });
        },
        loadGoogleMaps: function () {
            return $ocLazyLoad.load({
                name    : 'uiGmapgoogle-maps',
                files   : [BOWER_ROOT + 'angular-google-maps/dist/angular-google-maps.min.js'],
                cache   : true
            })

            .then(function () {
                $providerInjector.get('uiGmapGoogleMapApiProvider').configure({
                    key         : GMAPS_API_KEY,
                    v           : '3.24',
                    libraries   : 'geometry'
                });
            });
        },

        loadBEEPlugin: function () {
            return $ocLazyLoad.load('https://app-rsrc.getbee.io/plugin/BeePlugin.js')
        },

        loadPaymentHub: function () {
            return $ocLazyLoad.load('https://cdn.jsdelivr.net/npm/payment-hub-sdk@1.1.6/dist/payment-hub-sdk.umd.min.js')
        },

        loadDragDrop: function() {
            return $ocLazyLoad.load([
                    { files: [BOWER_ROOT + 'jquery-ui/jquery-ui.min.js'],
                      cache: true 
                    },
                    {
                      name : 'ngDragDrop',
                      files: [BOWER_ROOT + 'angular-dragdrop/src/angular-dragdrop.min.js'],
                      cache: true
                    }  
            ]);
        },

        loadMomentTimezone: function() {
            return $ocLazyLoad.load({
                files: _.map([
                    'moment-timezone/builds/moment-timezone-with-data.min.js',
                ], (path) => `${BOWER_ROOT}${path}`)
            })
        }
    };
});
