class FreeTicketService {
    constructor($http, $uibModal) {
        this.$http = $http;
        this.$uibModal = $uibModal;
    }

    get baseURL() {
        return '/api/event';
    }

    post(url, data) {
        return this.$http.post(url, data)
            .then(({ data }) => data)
            .catch(({ data }) => Promise.reject(data));
    }

    create(data, eventID) {
        return this.post(`${this.baseURL}/${eventID}/ticket/free`, data);
    }

    import(eventID, formData) {
        return this.$http.post(`${this.baseURL}/${eventID}/ticket/free-import`, formData, {
            withCredentials: true,
            headers: { 'Content-Type': undefined },
            transformRequest: angular.identity
        });
    }

    getImport(importID, eventID) {
        return this.$http.get(`/api/event/${eventID}/import/${importID}`)
            .then(({ data }) => data);
    }

    openGenerateFreeTicketModal(ticketType) {
        return this.$uibModal.open({
            size: 'md',
            template: `
                <free-ticket ticket-type="ticketType" on-close="$close()"></free-ticket>
            `,
            controller: ['$scope', function($scope) {
                $scope.ticketType = ticketType;
            }]
        })
    }
}

FreeTicketService.$inject = ['$http', '$uibModal'];

angular.module('SportWrench').service('FreeTicketService', FreeTicketService);
