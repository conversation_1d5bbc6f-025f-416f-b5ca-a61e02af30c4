class ExhibitorTicketService {
    constructor($http, $uibModal) {
        this.$http = $http;
        this.$uibModal = $uibModal;
    }

    get baseURL() {
        return '/api/event';
    }

    post(url, data) {
        return this.$http.post(url, data)
            .then(({ data }) => data)
            .catch(({ data }) => Promise.reject(data));
    }

    create(data, eventID) {
        return this.post(`${this.baseURL}/${eventID}/ticket/exhibitor`, data);
    }

    import(eventID, formData) {
        return this.$http.post(`${this.baseURL}/${eventID}/ticket/exhibitor-import`, formData, {
            withCredentials: true,
            headers: { 'Content-Type': undefined },
            transformRequest: angular.identity
        });
    }

    getImport(importID, eventID) {
        return this.$http.get(`${this.baseURL}/${eventID}/ticket/exhibitor-import/${importID}`)
            .then(({ data }) => data);
    }

    openGenerateExhibitorTicketModal(ticketType, reloadData) {
        return this.$uibModal.open({
            size: 'md',
            template: `
                <exhibitor-ticket ticket-type="ticketType" on-close="onClose()">></exhibitor-ticket>
            `,
            controller: ['$scope', '$uibModalInstance', function($scope, $uibModalInstance) {
                $scope.ticketType = ticketType;

                $scope.onClose = function () {
                    $uibModalInstance.close();
                    reloadData();
                }
            }]
        })
    }
}

ExhibitorTicketService.$inject = ['$http', '$uibModal'];

angular.module('SportWrench').service('ExhibitorTicketService', ExhibitorTicketService);
