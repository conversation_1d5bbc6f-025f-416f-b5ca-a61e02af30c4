angular.module('SportWrench').directive('tournamentDetailsForm', ['$timeout', function ($timeout) {
    return {
        restrict: 'E',
        scope: {
            tournament: '='
        },
        templateUrl: 'events/settings/general/forms/tournament-details-form.html',
        replace: true,
        require: '^generalSettings',
        link: function (scope, attrs, elem, ctrl) {
            scope.utils = {
                variationsLoading   : false,
                formSubmitted       : false,
                teams_enabled       : false // use this in watchers instead of allow_teams_registration
            };
            scope.sportInfo = {
                variations: []
            };

            scope.MAX_EVENT_SHORT_NAME_LENGTH = 20;

            applyDefaultValues(scope.tournament);

            var __loadSportVars = function (id) {
                if(!id) return;    
                scope.utils.variationsLoading = true;
                ctrl.loadSportVariations(id).success(function (data) {
                    scope.sportInfo.variations = data.variations;
                }).finally(function () {
                    scope.utils.variationsLoading = false;
                });
            };

            __loadSportVars(scope.tournament.sport_id);

            scope.$on('EventSettingsForm.Submitted', function () {
                scope.utils.formSubmitted = true;
                var errors = [], form = scope.detailsForm;
                if(form.long_name.$invalid)    
                    errors.push('Invalid Event Name');
                if(form.name.$invalid)
                    errors.push('Invalid Event Short Name');
                if(scope.validateEventPurpose())
                    errors.push('Choose Event Creation purpose');
                if(form.timezone.$invalid)
                    errors.push('Choose a Timezone');
                if(form.sport.$invalid)
                    errors.push('Choose an Event Sport');
                if(form.sport_variation.$invalid)
                    errors.push('Choose a Sport Variation');
                if(scope.tournament.date_start > scope.tournament.date_end) {
                    errors.push('Event Date Start is greater than Event Date End');
                }
                if(form.date_start.$invalid)
                    errors.push('Invalid Event Date Start');
                if(form.date_end.$invalid)
                    errors.push('Invalid Event Date End');

                ctrl.setDetailsFormErrors(errors);
            });

            scope.validateEventPurpose = function () {
                return !(scope.tournament.allow_teams_registration || 
                         scope.tournament.allow_ticket_sales || 
                         scope.tournament.has_officials ||
                         scope.tournament.has_exhibitors
                );
            };

            scope.loadSportInfo = function () {        
                ctrl.emitSportChanged();
                __loadSportVars(scope.tournament.sport_id);
            };

            scope.showHousingBox = function () {
                return this.tournament.allow_teams_registration && (this.tournament.sport_variation_id !== 3);
            };

            scope.dateStartChanged = function () {
                $timeout(function () {
                    if (!scope.validateDates()) {
                        return;
                    }
                    ctrl.emitDateStartChanged(scope.tournament.date_start);
                });               
            };

            scope.dateEndChanged = function () {
                $timeout(function () {
                    if (!scope.validateDates()) {
                        return;
                    }
                    ctrl.emitDateEndChanged(scope.tournament.date_end);
                }); 
            };

            scope.sportVariationChanged = function () {
                if(this.tournament.sport_variation_id === 3 && this.tournament.has_status_housing)
                    this.tournament.has_status_housing = false;
                ctrl.emitSportVariationChanged();
            };

            scope.validateDates = function () {
                var isValid = (scope.tournament.date_start <= scope.tournament.date_end);

                // Check if form controls exist before calling $setValidity
                if (scope.detailsForm && scope.detailsForm.date_start) {
                    scope.detailsForm.date_start.$setValidity('event-dates', isValid);
                }
                if (scope.detailsForm && scope.detailsForm.date_end) {
                    scope.detailsForm.date_end.$setValidity('event-dates', isValid);
                }

                return isValid;
            };

            scope.showTeamsSortBy = function () {
                return this.tournament.allow_teams_registration;
            };

            $timeout(function () {
                scope.validateDates();
            });

            var formName = 'details';

            ctrl.registerForm(formName);

            scope.$on('$destroy', function () {
                ctrl.unRegisterForm(formName);
            });
        }
    }
}]);

function applyDefaultValues(tournament) {
    tournament.teams_settings.sort_by = tournament.teams_settings.sort_by || 'seed_current';
}
