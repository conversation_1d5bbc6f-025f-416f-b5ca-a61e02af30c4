const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const glob = require('glob');

const entryFiles = glob.sync([
    './frontend/sport-wrench.module.js',
    './frontend/**/*.js',
    './assets/styles/main.scss',
    './assets/styles/admin.scss',
], { absolute: true });

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: entryFiles,

    plugins: [
        new HtmlWebpackPlugin({
            template: './frontend/index.html',
            filename: 'index.html',
            inject: 'body',
            scriptLoading: 'blocking',
            minify: false,
        }),
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend/', from: '**/*.html', to: '[path][name][ext]', globOptions: { ignore: ['**/index.html'] } },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8079,
    },
};
