// Vendor Dependencies Entry Point for Frontend Admin
// This file imports all vendor dependencies to create a vendor bundle

// Core AngularJS
import 'angular';
import 'angular-animate';
import 'angular-cookies';
import 'angular-resource';
import 'angular-route';
import 'angular-sanitize';

// UI and Routing
import 'angular-ui-router';
import 'angular-ui-bootstrap';

// UI Components
import 'ng-table/dist/ng-table.js';
import 'angular-toastr';
import 'ui-select';
import 'angular-bootstrap-colorpicker';
import 'angularjs-dropdown-multiselect';

// Utility Libraries
import 'jquery';
import 'underscore';
import 'moment';
import 'moment-timezone';

// Additional Angular modules
import 'angular-file-upload';
import 'angular-clipboard';
import 'angular-loading-bar';
import 'ngstorage';
import 'oclazyload';
import 'angular-simple-logger';

// Styles
import 'font-awesome/css/font-awesome.css';
import 'angular-loading-bar/build/loading-bar.min.css';
import 'ng-table/dist/ng-table.css';
import 'angular-toastr/dist/angular-toastr.css';
import 'ui-select/dist/select.css';
import 'angular-bootstrap-colorpicker/css/colorpicker.min.css';

// Bootstrap Sass (will be imported in main SCSS files)
// import 'bootstrap-sass/assets/stylesheets/_bootstrap.scss';
